================================================================================
SIR模型比较分析报告
================================================================================
生成时间: 2025-08-24 12:02:22

模型说明:
1. 常系数模型: dS/dt = -β·S·I/N, dI/dt = β·S·I/N - γ·I, dR/dt = γ·I
2. 线性变系数模型: dS/dt = -λ(t)·I·S, dI/dt = λ(t)·I·S - μ(t)·I, dR/dt = μ(t)·I
   其中 λ(t) = λ₀ + λ₁·t, μ(t) = μ₀ + μ₁·t
3. 指数变系数模型: dS/dt = -λ(t)·I·S, dI/dt = λ(t)·I·S - μ(t)·I, dR/dt = μ(t)·I
   其中 λ(t) = λ₀·exp(λ₁·t), μ(t) = μ₀·exp(μ₁·t)
4. 多项式变系数模型: dS/dt = -λ(t)·I·S, dI/dt = λ(t)·I·S - μ(t)·I, dR/dt = μ(t)·I
   其中 λ(t) = λ₀ + λ₁·t + λ₂·t², μ(t) = μ₀ + μ₁·t + μ₂·t²

最佳模型总结:
----------------------------------------
总群体: exponential模型 (MSE: 0.00066688)

------------------------------------------------------------
总群体 详细比较结果
------------------------------------------------------------

1. exponential模型:
   MSE: 0.00066688
   模型类型: 指数变系数模型
   拟合后的率函数:
     传播率: λ(t) = 0.334274·exp(-0.100000·t)
     恢复率: μ(t) = 0.021563·exp(-0.008605·t)
   拟合后的微分方程:
     dS/dt = -λ(t)·I·S = -0.334274·exp(-0.100000·t)·I·S
     dI/dt = λ(t)·I·S - μ(t)·I = 0.334274·exp(-0.100000·t)·I·S - 0.021563·exp(-0.008605·t)·I
     dR/dt = μ(t)·I = 0.021563·exp(-0.008605·t)·I
   平均R₀: 4.294094
   原始参数: [0.33427375955167665, -0.1, 0.021563433545154477, -0.008605080876351595]
   R² (S,I,R): (0.9822, 0.9407, 0.9787)
   ★ 最佳模型

2. linear模型:
   MSE: 0.00178092
   模型类型: 线性变系数模型
   拟合后的率函数:
     传播率: λ(t) = 0.247565 + -0.009898·t
     恢复率: μ(t) = 0.027180 + -0.000460·t
   拟合后的微分方程:
     dS/dt = -λ(t)·I·S = -(0.247565 + -0.009898·t)·I·S
     dI/dt = λ(t)·I·S - μ(t)·I = (0.247565 + -0.009898·t)·I·S - (0.027180 + -0.000460·t)·I
     dR/dt = μ(t)·I = (0.027180 + -0.000460·t)·I
   平均R₀: 4.208831
   原始参数: [0.24756488478547348, -0.009898265419473279, 0.027180410560219897, -0.0004600758468844988]
   R² (S,I,R): (0.9442, 0.8501, 0.9637)

3. polynomial模型:
   MSE: 0.00268826
   模型类型: 多项式变系数模型
   拟合后的率函数:
     传播率: λ(t) = 0.216058 + 0.006273·t + -0.000908·t²
     恢复率: μ(t) = 0.068065 + -0.004781·t + 0.000094·t²
   拟合后的微分方程:
     dS/dt = -λ(t)·I·S = -(0.216058 + 0.006273·t + -0.000908·t²)·I·S
     dI/dt = λ(t)·I·S - μ(t)·I = (0.216058 + 0.006273·t + -0.000908·t²)·I·S - (0.068065 + -0.004781·t + 0.000094·t²)·I
     dR/dt = μ(t)·I = (0.068065 + -0.004781·t + 0.000094·t²)·I
   平均R₀: 3.446578
   原始参数: [0.2160577537170118, 0.006273291000366447, -0.0009083127322680989, 0.06806458501630731, -0.004781425665635161, 9.440953212326991e-05]
   R² (S,I,R): (0.9104, 0.8340, 0.8882)

4. constant模型:
   MSE: 0.01777724
   模型类型: 常系数模型
   拟合后的率函数:
     传播率: β = 0.114880
     恢复率: γ = 0.031145
   拟合后的微分方程:
     dS/dt = -0.114880·S·I/N
     dI/dt = 0.114880·S·I/N - 0.031145·I
     dR/dt = 0.031145·I
   R₀: 3.688516
   R² (S,I,R): (0.3881, -0.4894, 0.8379)

============================================================
模型选择建议
============================================================
最佳模型: exponential模型

建议:
1. exponential变系数模型表现最佳，说明传播动力学存在时变特征
2. 变系数模型能更好地捕捉传播过程中的动态变化
3. 建议在实际应用中使用变系数模型进行预测和分析
