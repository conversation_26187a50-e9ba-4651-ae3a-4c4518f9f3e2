{"metadata": {"generated_at": "2025-08-24T11:59:27.887730", "description": "SIR model fitting results for virtual community experiment", "model_description": "SIR (Susceptible-Infected-Recovered) epidemic model", "fitting_method": "Least squares optimization with scipy.optimize.minimize"}, "fitting_results": {"total": {"beta": 0.13066335944742244, "gamma": 0.04298938480723333, "R0": 3.0394331073432164, "mse": 0.017145420485155015, "r2_susceptible": 0.522200823838069, "r2_infected": -0.12488120752493348, "r2_recovered": 0.8491862717471952, "fitted_curves": {"time": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40], "susceptible": [0.94, 0.932360944539958, 0.924165276624047, 0.9153879546885683, 0.9060053854916332, 0.8959959753528753, 0.885340656904724, 0.8740234795053277, 0.8620322089116184, 0.8493588684395665, 0.836000510760321, 0.8219595852216653, 0.8072445456570185, 0.7918702725594832, 0.7758583913905551, 0.7592374800446958, 0.7420431238299162, 0.724317803755108, 0.7061106348872809, 0.6874768543763349, 0.6684772278581491, 0.6491772340512223, 0.6296461234092131, 0.6099558582255293, 0.5901799788346275, 0.570392424987467, 0.5506663572848768, 0.531073023527929, 0.5116806960140003, 0.4925537392528112, 0.47375177833197374, 0.45532905385315636, 0.4373339331498211, 0.41980858374423696, 0.402788819807591, 0.38630409770831864, 0.37037764125311157, 0.35502669281688304, 0.34026285433711884, 0.32609250231257675], "infected": [0.06, 0.06495439311012555, 0.07024520928646738, 0.07588281213134405, 0.08187569791570824, 0.0882300421417885, 0.09494928812107888, 0.1020336837550315, 0.10947982751551465, 0.11728027655767805, 0.12542300150992788, 0.13389117921292573, 0.1426628175415925, 0.1517105595672742, 0.16100159586436086, 0.17049769195761658, 0.1801553635398264, 0.18992620674222227, 0.1997573637282774, 0.20959220893024375, 0.21937109088956427, 0.22903225809391092, 0.23851286999815785, 0.24775007883629993, 0.25668214110036525, 0.2652495246977934, 0.2733959691938207, 0.28106945749020057, 0.28822307761984645, 0.2948157187667485, 0.3008126426001005, 0.306185844710449, 0.3109142488927477, 0.3149837355546398, 0.3183869979557796, 0.3211232598342134, 0.32319787728986726, 0.32462183463367794, 0.3254111735874575, 0.32558637389217715], "recovered": [0.0, 0.0026846623499164877, 0.005589514089485463, 0.008729233180087559, 0.012118916592658378, 0.015773982505335973, 0.019710054974196835, 0.023942836739640597, 0.02848796357286666, 0.03336085500275514, 0.038576487729750746, 0.04414923556540859, 0.050092636801388764, 0.056419167873242325, 0.06314001274508382, 0.07026482799768724, 0.0778015126302571, 0.08575598950266938, 0.09413200138444128, 0.10293093669342099, 0.11215168125228629, 0.12179050785486643, 0.1318410065926287, 0.14229406293817048, 0.15313788006500703, 0.16435805031473927, 0.17593767352130218, 0.18785751898187017, 0.20009622636615287, 0.21263054198043999, 0.2254355790679255, 0.23848510143639434, 0.2517518179574309, 0.265207680701123, 0.27882418223662914, 0.2925726424574678, 0.30642448145702106, 0.32035147254943885, 0.33432597207542347, 0.34832112379524593]}, "equations": {"population": "总群体", "parameters": {"beta": 0.13066335944742244, "gamma": 0.04298938480723333, "R0": 3.0394331073432164}, "differential_equations": {"dS_dt": "dS/dt = -β·S·I/N = -0.1307·S·I/N", "dI_dt": "dI/dt = β·S·I/N - γ·I = 0.1307·S·I/N - 0.0430·I", "dR_dt": "dR/dt = γ·I = 0.0430·I"}, "mathematical_form": {"dS_dt": "-0.1307 * S * I / N", "dI_dt": "0.1307 * S * I / N - 0.0430 * I", "dR_dt": "0.0430 * I"}}}}}